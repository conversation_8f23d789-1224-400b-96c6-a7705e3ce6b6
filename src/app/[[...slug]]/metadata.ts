import slugify from 'slugify';

import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { authorToUrl } from 'themes/autumn/components/stories/AuthorLink';
import {
  getAuthorTwitterUsername,
  getStoryTags,
} from 'themes/autumn/templates/stories/common';
import { StoryViewType } from 'types/ZoneItems';
import { getAdCats } from 'util/ads';
import { htmlToText, stripTags } from 'util/device';
import {
  TransformOutputFormat,
  authorImageUrl,
  hasExternalURI,
  hasValidURI,
  storyImageUrl,
} from 'util/image';
import { getPageHierarchy, pageUrlPath, seoUrlPath } from 'util/page';
import { getAppId, titleCase } from 'util/string';
import { getUgcMetadata } from 'util/ugc';

import type { Metadata } from 'next';
import type { Robots } from 'next/dist/lib/metadata/types/metadata-types';
import type { RootState } from 'store/store';

const OG_IMAGE_SIZE = [1200, 630];
const MAX_DESCRIPTION_LENGTH = 160;

export default function getMetadata(context: RootState): Metadata {
  const { transformUrl } = context.settings;
  const metadata: Metadata = {
    metadataBase: new URL(`https://${context.conf.domain}`),
    openGraph: {
      locale: 'en_AU',
      siteName: context.conf.name,
      type: 'website',
    },
    other: {},
    robots: {},
    twitter: {
      card: 'summary_large_image',
      site: `@${context.conf.twitterUsername}`,
    },
  };

  const isHomePage = context.settings.viewType;

  // Domain verification
  if (isHomePage) {
    const {
      domainVerifyCodeBing,
      domainVerifyCodeFacebook,
      domainVerifyCodeGoogle,
    } = context.conf;

    metadata.other ??= {};

    if (domainVerifyCodeFacebook) {
      metadata.other['facebook-domain-verification'] =
        domainVerifyCodeFacebook;
    }

    if (domainVerifyCodeBing) {
      metadata.other['msvalidate.01'] = domainVerifyCodeBing;
    }

    if (domainVerifyCodeGoogle) {
      metadata.other['google-site-verification'] = domainVerifyCodeGoogle;
    }
  }

  // Mobile app
  const { mobileApp } = context.features;
  if (mobileApp.enabled && mobileApp.data.appStoreId.length) {
    metadata.itunes = { appId: getAppId(mobileApp.data.appStoreId) };
  }

  // Viafoura
  const storyId = context.story?.id;
  if (
    storyId &&
    context.features.viafoura.enabled &&
    context.features.viafoura.data.sectionUuid
  ) {
    metadata.other ??= {};
    metadata.other['vf:container_id'] = storyId;
  }

  const { primaryPage, secondaryPage } = getPageHierarchy(
    context.conf.topDownAdCatTargeting,
    context.pages,
  );

  // Piano cXense
  if (context.pages) {
    const primaryPageName = primaryPage?.name || '';
    const secondaryPageName = secondaryPage?.name || '';
    const cXenseTaxonomy =
      `${slugify(primaryPageName).toLowerCase()}/${slugify(
        secondaryPageName,
      ).toLowerCase()}`.replace(/^\/|\/$/g, '');
    if (!isHomePage && context.features.piano.enabled && cXenseTaxonomy) {
      metadata.other ??= {};
      metadata.other['cXenseParse:taxonomy'] = cXenseTaxonomy;
    }
  }

  // Publift meta tags
  if (
    context.features.adServing.enabled &&
    context.features.adServing.data.usePublift &&
    context.page
  ) {
    const { cat, cat1, cat2, cat3, cat4 } = getAdCats({
      adServingEnabled: true,
      currentCategorySlug: context.classifieds.category?.slug,
      isAuthorPage: !!context.author?.id,
      isBrandedStory:
        !!context.story?.id &&
        (context.settings.viewType as StoryViewType) ===
          StoryViewType.STORY_BRANDED,
      pages: context.pages,
      siteDoubleClickCat: context.features.adServing.data.doubleClickCat,
      topDownAdCatTargeting: context.conf.topDownAdCatTargeting,
    });
    metadata.other ??= {};
    let { doubleClickSite } = context.features.adServing.data;
    if (new URLSearchParams(context.settings.queryString).get('adcallkw')) {
      doubleClickSite = `onl.adtester/${doubleClickSite}`;
    }
    metadata.other.site = doubleClickSite;
    metadata.other['category-l1'] = cat;
    metadata.other['category-l2'] = cat1;
    metadata.other['category-l3'] = cat2;
    if (context.conf.topDownAdCatTargeting) {
      metadata.other['category-l4'] = cat3;
      metadata.other['category-l5'] = cat4;
    }
  }

  // Shortcut icons
  const { staticSiteUrl } = context.settings;
  const { logoSvgSquare } = context.conf;
  metadata.icons = {
    other: [
      {
        rel: 'apple-touch-icon-precomposed',
        url: `${staticSiteUrl}ico/main-fav.png`,
      },
      {
        rel: 'apple-touch-icon-precomposed',
        sizes: '114x114',
        url: `${staticSiteUrl}ico/apple-touch-icon-114x114-precomposed.png`,
      },
      {
        rel: 'apple-touch-icon-precomposed',
        sizes: '72x72',
        url: `${staticSiteUrl}ico/apple-touch-icon-72x72-precomposed.png`,
      },
      {
        rel: 'apple-touch-icon-precomposed',
        url: `${staticSiteUrl}ico/apple-touch-icon-precomposedv.png`,
      },
    ],
    shortcut: [
      {
        type: 'image/vnd.microsoft.icon',
        url: `${staticSiteUrl}ico/favicon.ico`,
      },
      {
        type: 'image/svg',
        url: logoSvgSquare,
      },
    ],
  };

  const titleParts = [context.conf.name, context.conf.location];

  // Page
  if (context.page) {
    const { noIndex } = context.page;
    const { host } = context.settings;
    const pageUrl = context.page.url || '';
    const fullPageUrl = seoUrlPath(`https://${host}${pageUrlPath(pageUrl)}`);
    metadata.alternates = {
      canonical: !noIndex ? fullPageUrl : undefined,
    };
    metadata.description = context.page.metaDescription;
    metadata.robots = {
      nosnippet: context.page.noSnippet,
    };
    if (!storyId) {
      metadata.robots.index = !noIndex;
    }
    if (context.page.openGraphImage && metadata.openGraph && (!metadata.openGraph.images || (Array.isArray(metadata.openGraph.images) && metadata.openGraph.images.length === 0))) {
      metadata.openGraph.images = [
        {
          url: context.page.openGraphImage,
        },
      ];
    }
    titleParts.unshift(context.page.metaTitle || context.page.name);
  }

  // Author page
  const { author } = context;
  if (author) {
    const siteName = context.conf.name;
    const [firstName, ...lastName] = author.name.split(' ');

    const strippedBio = htmlToText(author.bio || '');
    const bioLength = strippedBio.length;

    const description =
      bioLength > MAX_DESCRIPTION_LENGTH
        ? `${strippedBio.substring(0, MAX_DESCRIPTION_LENGTH)}...`
        : strippedBio;

    metadata.description = description || `Author profile of ${siteName}`;
    titleParts.unshift(`${author.name}'s Profile`);

    const authorImageDiameter = 200;
    const authorImage = author.mugshot
      ? authorImageUrl({
          height: authorImageDiameter,
          image: {
            uri: author?.mugshot,
          },
          outputFormat: TransformOutputFormat.WEBP,
          transformUrl,
          width: authorImageDiameter,
        })
      : undefined;
    metadata.openGraph = {
      firstName,
      images: authorImage
        ? [
            {
              height: authorImageDiameter,
              type: 'image/webp',
              url: authorImage,
              width: authorImageDiameter,
            },
          ]
        : undefined,
      lastName: lastName.join(' '),
      type: 'profile',
    };

    if (author.twitter) {
      metadata.twitter ??= {};
      metadata.twitter.creator = author.twitter;
    }
  }

  // Story page
  if (storyId) {
    const { story } = context;
    const fullDomain = `https://${context.conf.domain}`;
    const seoArticleTags = getStoryTags(story);
    metadata.other ??= {};
    metadata.other.news_keywords = seoArticleTags.join(',');
    const { location } = context.conf;
    if (context.features.facebookNews.enabled) {
      metadata.other['fb:app_id'] = context.features.facebookNews.data.appId;
      metadata.other['article:opinion'] = story.isOpinion.toString();
      metadata.other['article:content_tier'] = story.contentTier;
      metadata.other['article:location'] = location;
    }
    const currentStoryUrl = seoUrlPath(`${fullDomain}${story.storyUrl}`);
    const seoAbsoluteUrl = seoUrlPath(story.canonicalUrl) || currentStoryUrl;

    const seoTitle = story.seoTitle || story.title;
    const { seoDescription } = story;
    const pageName = context.page?.name ?? '';
    titleParts[0] = seoTitle || pageName;
    const isViafouraEnabled =
      context.features.viafoura.enabled &&
      context.features.viafoura.data.sectionUuid;

    // Add the `vf:url` metatag if different from the canonical URL. This must
    // be present on page load, and not dynamically as part of the Viafoura
    // component, in order to use it instead of the canonical tag
    if (isViafouraEnabled && currentStoryUrl !== seoAbsoluteUrl) {
      metadata.other['vf:url'] = currentStoryUrl;
    }
    if (context.conf.useCleanStoryUrl || !context.page?.noIndex) {
      metadata.alternates ??= {};
      metadata.alternates.canonical = seoAbsoluteUrl;
    }
    metadata.description = seoDescription || story.summary;
    metadata.robots ??= {};
    // Story with feature flag useCleanStoryUrl
    // - will have clean URLs throughout the site.
    // - the canonical URL will be driving the Google crawling.
    // - story pages will not have meta robots.
    // Reasoning - after discussing with SEO expert
    // * In the presence of canonical URLs, we can let go of meta robots.
    // * By default, the meta robots are INDEX, FOLLOW.
    // * If meta robots are present, we are explicitly stating
    //    Google to take action, i.e. INDEX or NOINDEX.
    // eslint-disable-next-line @stylistic/max-len
    // https://developers.google.com/search/docs/crawling-indexing/consolidate-duplicate-urls#best-practices
    if (!context.conf.useCleanStoryUrl) {
      (metadata.robots as Robots).index =
        !context.page?.noIndex && story.publishable && !story.storySeoNoIndex;
    }
    metadata.openGraph = {
      authors: story.authors.length
        ? story.authors.map((a) => `${fullDomain}${authorToUrl(a)}`)
        : [titleCase(story.byline)],
      description: story.summary,
      images:
        story.leadImage && hasValidURI(story.leadImage)
          ? [
              {
                alt:
                  story.leadImage.description ||
                  story.leadImage.title ||
                  seoTitle,
                height: OG_IMAGE_SIZE[1],
                type: 'image/jpeg',
                url: hasExternalURI(story.leadImage)
                  ? story.leadImage.uri
                  : `${
                      !transformUrl.startsWith('https') ? fullDomain : ''
                    }${storyImageUrl({
                      height: OG_IMAGE_SIZE[1],
                      image: story.leadImage,
                      transformUrl,
                      width: OG_IMAGE_SIZE[0],
                    })}`,
                width: OG_IMAGE_SIZE[0],
              },
            ]
          : [],
      modifiedTime: story.updatedOn,
      publishedTime: story.publishFrom,
      section: pageName,
      tags: seoArticleTags,
      title: story.title,
      type: 'article',
      url: seoAbsoluteUrl,
    };
    metadata.twitter ??= {};
    metadata.twitter.creator = getAuthorTwitterUsername(story.authors);
  }

  // Classifieds
  const { classifieds } = context;
  if (classifieds.ad || classifieds.ads) {
    metadata.alternates = {
      canonical: classifieds.ad ? classifieds.ad.canonicalUrl : undefined,
    };
    let metaDescription: string | undefined;

    const primaryPageUrl = primaryPage?.url;
    const primaryPageSlug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';
    const { category } = context.classifieds;
    if (classifieds.ad) {
      // 320 is recommended maximum length for SEO
      metaDescription = stripTags(classifieds.ad.text).substring(0, 320);
      // Generic description for category pages, but use page's description for
      // tributes page
    } else if (
      category &&
      primaryPageSlug &&
      primaryPageSlug !== TRIBUTES_FUNERALS_SLUG
    ) {
      metaDescription = category.description;
    }
    // Remove extraneous spaces and linebreaks
    metadata.description = metaDescription?.replace(/\s+/g, ' ');

    const isEmptyCategory =
      !!classifieds.ads && classifieds.ads.data.length === 0;
    metadata.robots = {
      index: !isEmptyCategory,
    };
    const title =
      classifieds.ad?.title ||
      classifieds.subcategory?.name ||
      classifieds.category?.name;

    if (title) {
      titleParts.unshift(title);
    }

    if (classifieds.ad) {
      metadata.openGraph = {
        images: classifieds.ad.images.filter(Boolean).map((uri) => ({
          url: storyImageUrl({
            image: {
              uri,
            },
            transformUrl,
            width: 640,
          }),
        })),
      };
    }
  }

  // User Generated Content
  const { ugc } = context;

  if (ugc?.ugcDetail?.id) {
    const { description: metaDescription, title: metaTitle } = getUgcMetadata(
      ugc.ugcDetail,
    );

    metadata.title = metaTitle;
    metadata.description = metaDescription;
    metadata.alternates ??= {};
    // eslint-disable-next-line @stylistic/max-len
    metadata.alternates.canonical = `https://${context.conf.domain}${ugc.ugcDetail.canonicalUrl}/`;
  } else {
    metadata.title = titleParts.filter((t) => !!t).join(' | ');
  }

  return metadata;
}
