import React from 'react';

import { useAppSelector } from 'store/hooks';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import PageNavigation from 'themes/autumn/components/page/PageNavigation';
import PageParentHeading from 'themes/autumn/components/page/PageParentHeading';
import Zone from 'themes/autumn/components/zone/Zone';
import IndexPageRHC from 'themes/autumn/templates/index/IndexPage/IndexPageRHC';
import { NavigationType } from 'types/Nav';
import { ZoneName } from 'types/ZoneItems';
import { usePageParents, usePages } from 'util/hooks';
import { pageUrlPath } from 'util/page';

import type { NavThemeRecord } from 'types/Nav';

export const COMMUNITY_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-gray-900',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
};

export const classifiedsPage = {
  menuName: 'Classifieds',
  name: 'Classifieds',
  newWindow: false,
  url: 'classifieds',
};

export const tributesFuneralsPage = {
  menuName: 'Tributes & Funerals',
  name: 'Tributes & Funerals',
  newWindow: false,
  url: 'tributes-funerals',
};

function IndexPage(): React.ReactElement {
  const page = useAppSelector((state) => state.page);
  const { parent: parentPage } = usePageParents();
  const pages = usePages();

  return (
    <TemplateWrapper showNavigationAd showRevBanner showStickyFooterAd>
      <Container className="mt-4 md:mt-10" noGutter>
        <Zone name={ZoneName.TOP} />
        <div className="mx-4 overflow-y-visible md:mx-6 xl:mx-0">
          {parentPage?.name && parentPage?.url && (
            <PageParentHeading
              className="w-full"
              show={page.showHeading}
              text={parentPage.name}
              url={pageUrlPath(parentPage.url)}
            />
          )}
          <PageHeading
            className="mb-4 grow md:mb-5"
            show={page.showHeading}
            text={page.name}
          />
          <PageNavigation
            currentAsHeading
            desktopMoreOptionEnabled
            fontStyle="text-sm font-medium normal-case"
            mobileMoreOptionEnabled={false}
            moreOptionLabel="More"
            navClassName=""
            navWrapperClassName="border-gray-300 md:block"
            navigationType={NavigationType.Pill}
            pages={pages}
            shortcutPages={[tributesFuneralsPage, classifiedsPage]}
            theme={COMMUNITY_NAV_THEME}
          />
          <Zone name={ZoneName.MAIN_TOP} />
          <div className="mb-1.5 mt-7 hidden border-gray-300 md:block md:border-b-1" />
        </div>
        <div className="mx-4 md:mx-6 xl:mx-0">
          <Zone name={ZoneName.MAIN_TOP_FULL_WIDTH} />
        </div>
        <div className="mx-4 md:mx-6 lg:flex xl:mx-0">
          <div className="w-full lg:w-7/10">
            <div className="lg:pr-7">
              <Zone name={ZoneName.MAIN} />
            </div>
          </div>
          <IndexPageRHC className="gap-10 pt-5.5 lg:pl-7" />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}

export default IndexPage;
