import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useSearchParams } from 'next/navigation';
import React from 'react';

import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { UgcContentType } from 'types/ugc';
import { useLoadingMoreUgcItems } from 'util/hooks';
import { fetchCategories } from 'util/ugc';

import WhatsOn from '.';

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

jest.mock('store/hooks', () => ({
  useAppSelector: jest.fn(),
}));

jest.mock('util/hooks', () => ({
  useLoadingMoreUgcItems: jest.fn(),
  useWindowSize: jest.fn().mockReturnValue({
    height: 768,
    outerHeight: 768,
    outerWidth: 1024,
    width: 1024,
  }),
}));

jest.mock('util/ugc', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue([]),
  fetchCategories: jest.fn().mockResolvedValue([]),
}));

describe('WhatsOn with filters', () => {
  const mockZoneItemData = {
    limit: 1,
    offset: 1,
    pinnedUgcIds: [],
    pinnedUgcOnly: false,
    template: 'WhatsOn',
    title: 'Test Title',
    totalUgc: 1,
    ugc: [
      {
        canonicalUrl: '/test-event',
        categoryName: 'Arts',
        contentType: UgcContentType.EVENT,
        description: 'Test event description',
        endDatetime: '2025-01-01T12:00:00',
        id: 1,
        images: ['test-image.jpg'],
        lat: '',
        lng: '',
        location: 'Test Location',
        masthead: 1,
        nextOccurrence: '2025-01-01T12:00:00',
        organiserDetails: {
          contactNumber: '123-456-7890',
          email: '<EMAIL>',
          logo: 'logo.jpg',
          name: 'Test Organizer',
          websiteUrl: 'https://example.com',
        },
        priceText: 'Free',
        publishedOn: '2025-01-01T12:00:00',
        recurrenceText: 'Weekly',
        startDatetime: '2025-01-01T12:00:00',
        startTimeText: '6:00 PM',
        title: 'Test Event',
        userDetails: {
          avatar: 'avatar.jpg',
          userEmail: '<EMAIL>',
          userName: 'Test User',
        },
      },
    ],
    ugcListId: 1,
  };

  const mockCategories = [
    { id: '1', name: 'Arts' },
    { id: '2', name: 'Sports' },
  ];

  const mockLoadingMoreUgcItems = {
    loadMore: jest.fn(),
    loadedUgcItems: [],
    loadedUgcLimit: 10,
    loading: false,
    noMoreUgcItems: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mock implementations
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
    (useLoadingMoreUgcItems as jest.Mock).mockReturnValue(
      mockLoadingMoreUgcItems,
    );
    (fetchCategories as jest.Mock).mockResolvedValue(mockCategories);

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { href: 'https://example.com' },
      writable: true,
    });
  });

  it('renders without crashing', () => {
    render(
      <WhatsOn
        elementId={1}
        index={0}
        order={0}
        zoneItemData={mockZoneItemData}
        zoneItemId={1}
        zoneItemType={ZoneItemType.UgcList}
        zoneName={ZoneName.MAIN}
      />,
    );
    expect(screen.getByText('Filter by')).toBeInTheDocument();
  });

  it('fetches categories on mount', async () => {
    render(
      <WhatsOn
        elementId={1}
        index={0}
        order={0}
        zoneItemData={mockZoneItemData}
        zoneItemId={1}
        zoneItemType={ZoneItemType.UgcList}
        zoneName={ZoneName.MAIN}
      />,
    );
    expect(fetchCategories).toHaveBeenCalledWith('event');
    await waitFor(() => {
      expect(fetchCategories).toHaveBeenCalledTimes(1);
    });
  });

  it('displays filter options', async () => {
    render(
      <WhatsOn
        elementId={1}
        index={0}
        order={0}
        zoneItemData={mockZoneItemData}
        zoneItemId={1}
        zoneItemType={ZoneItemType.UgcList}
        zoneName={ZoneName.MAIN}
      />,
    );

    await waitFor(() => {
      expect(fetchCategories).toHaveBeenCalledTimes(1);
    });

    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Event Type')).toBeInTheDocument();
    expect(screen.getByText('Place')).toBeInTheDocument();
  });

  it('shows "no results" message when searching with no results', () => {
    (useSearchParams as jest.Mock).mockReturnValue(
      new URLSearchParams('?category=arts'),
    );

    (useLoadingMoreUgcItems as jest.Mock).mockReturnValue({
      ...mockLoadingMoreUgcItems,
      loadedUgcItems: [],
    });

    render(
      <WhatsOn
        elementId={1}
        index={0}
        order={0}
        zoneItemData={mockZoneItemData}
        zoneItemId={1}
        zoneItemType={ZoneItemType.UgcList}
        zoneName={ZoneName.MAIN}
      />,
    );

    expect(
      screen.getByText(/Sorry, we didn’t find any results/i),
    ).toBeInTheDocument();
    expect(screen.getByText('Clear search filters')).toBeInTheDocument();
  });

  it('loads more items when clicking "Load More"', async () => {
    const loadMore = jest.fn();
    (useLoadingMoreUgcItems as jest.Mock).mockReturnValue({
      ...mockLoadingMoreUgcItems,
      loadMore,
      loadedUgcItems: [{ id: '2', title: 'Test Event' }],
    });

    render(
      <WhatsOn
        elementId={1}
        index={0}
        order={0}
        zoneItemData={mockZoneItemData}
        zoneItemId={1}
        zoneItemType={ZoneItemType.UgcList}
        zoneName={ZoneName.MAIN}
      />,
    );

    const loadMoreButton = screen.getByTestId('load-button');
    fireEvent.click(loadMoreButton);

    await waitFor(() => {
      expect(loadMore).toHaveBeenCalled();
    });
  });
});
